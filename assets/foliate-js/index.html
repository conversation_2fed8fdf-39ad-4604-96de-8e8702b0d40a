<!DOCTYPE html>
<html lang="en">
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
<title>Anx Reader</title>
<style>
  html {
    height: 100vh;
  }

  body {
    -webkit-user-select: none;
    user-select: none;
    margin: 0 !important;
    height: 100vh;
  }


  #footnote-dialog {
    /*
    padding: var(safe-area-inset-top) var(safe-area-inset-right) var(safe-area-inset-bottom) var(safe-area-inset-left);
    */
    position: fixed;
    width: 80vw;
    height: 80vh;
    max-width: 400px;
    max-height: 200px;
    min-width: 300px;
    min-height: 200px;
    border-radius: 15px;
    border: 1px solid grey;
    -webkit-user-select: none;
    user-select: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    outline: none;
    z-index: 1000;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
  }

  #footnote-dialog main {
    overflow: auto;
    width: 100%;
    height: 100%;
  }
</style>
<div id="footnote-dialog">
  <main></main>
</div>
<script>
  console.log("AnxUA", navigator.userAgent);
</script>
<script src="./dist/bundle.js" type="module"></script>
<script src="./dist/pdf-legacy.js"></script>
<script>
// Essential callFlutter function for DassoShu Reader (from anx-reader-origin)
const callFlutter = (name, data) => {
  // console.log('callFlutter', name, data)
  window.flutter_inappwebview.callHandler(name, data)
}

// Bookmark visual indicator functions (adapted from anx-reader-origin)
const showBookmarkIndicator = (deltaY = 60) => {
  let bookmarkIcon = document.getElementById('bookmark-icon');

  const bookMarkSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 24"><g data-name="Layer 2"><g data-name="bookmark"><rect width="8" height="24" opacity="0"/><path d="M2 21a1 1 0 0 1-.49-.13A1 1 0 0 1 1 20V5.33A2.28 2.28 0 0 1 3.2 3h1.6A2.28 2.28 0 0 1 7 5.33V20a1 1 0 0 1-.5.86 1 1 0 0 1-1 0L4 19.07l-1.5 1.79A1 1 0 0 1 2 21z" fill="#215a8f"/></g></g></svg>`

  if (!bookmarkIcon) {
    bookmarkIcon = document.createElement('div');
    bookmarkIcon.id = 'bookmark-icon';
    bookmarkIcon.innerHTML = bookMarkSvg;
    bookmarkIcon.style.cssText = `
      height: 80px;
      width: 26px;
      position: fixed;
      top: -16px;
      right: 20px;
      display: flex;
      opacity: 0;
      transition: opacity 0.2s ease;
      z-index: 1000;
      pointer-events: none;
    `;
    document.body.appendChild(bookmarkIcon);
  }

  const opacity = Math.min(deltaY / 60, 1);
  bookmarkIcon.style.opacity = opacity;
}

const hideBookmarkIndicator = () => {
  const bookmarkIcon = document.getElementById('bookmark-icon');
  if (bookmarkIcon) {
    bookmarkIcon.style.transition = 'opacity 0.3s ease-out';
    bookmarkIcon.style.opacity = '0';

    setTimeout(() => {
      if (bookmarkIcon && bookmarkIcon.parentNode) {
        bookmarkIcon.parentNode.removeChild(bookmarkIcon);
      }
    }, 300);
  }
}

// Enhanced bookmark functionality for DassoShu Reader
window.checkCurrentPageBookmarkEnhanced = () => {
  if (!reader || !reader.view || !reader.view.lastLocation) return

  const currentCfi = reader.view.lastLocation.cfi
  if (!currentCfi) return

  // Call Flutter to check if bookmark exists at current location
  callFlutter('checkBookmarkEnhanced', {
    cfi: currentCfi,
    fraction: reader.view.lastLocation.fraction || 0
  })
}

// Enhanced bookmark checking function that compares CFI ranges
window.checkBookmarkAtCfi = (bookmarkCfi, currentCfi) => {
  if (!bookmarkCfi || !currentCfi) return false

  try {
    // Simple CFI comparison - if they match or are very close, consider it a match
    const bookmarkBase = bookmarkCfi.split('!')[0] || bookmarkCfi
    const currentBase = currentCfi.split('!')[0] || currentCfi

    return bookmarkBase === currentBase || bookmarkCfi === currentCfi
  } catch (e) {
    console.warn('Error comparing CFIs:', e)
    return bookmarkCfi === currentCfi
  }
}

// Add missing removeBookmarkAtCurrentLocation function
window.removeBookmarkAtCurrentLocation = () => {
  if (!reader || !reader.view || !reader.view.lastLocation) return

  const currentCfi = reader.view.lastLocation.cfi
  if (!currentCfi) return

  // Call Flutter to remove bookmark at current location
  callFlutter('removeBookmark', currentCfi)
}

// Expose bookmark indicator functions globally
window.showBookmarkIndicator = showBookmarkIndicator
window.hideBookmarkIndicator = hideBookmarkIndicator
</script>