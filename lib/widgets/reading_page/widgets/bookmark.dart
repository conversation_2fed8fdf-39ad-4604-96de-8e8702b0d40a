import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/bookmark.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/providers/bookmark.dart';
import 'package:dasso_reader/utils/error_handler.dart';
import 'package:dasso_reader/widgets/common/layout_utils.dart';
import 'package:dasso_reader/widgets/delete_confirm.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BookmarkWidget extends ConsumerStatefulWidget {
  const BookmarkWidget({super.key, required this.epubPlayerKey});

  final GlobalKey<EpubPlayerState> epubPlayerKey;

  @override
  ConsumerState<BookmarkWidget> createState() => _BookmarkWidgetState();
}

class _BookmarkWidgetState extends ConsumerState<BookmarkWidget> {
  @override
  Widget build(BuildContext context) {
    final bookId = widget.epubPlayerKey.currentState!.book.id;

    final bookmarkList = ref.watch(bookmarkProvider(bookId));
    return bookmarkList.when(
      data: (bookmarks) {
        if (bookmarks.isEmpty) {
          return Center(
            child: Column(
              children: [
                Text(
                  L10n.of(context).no_bookmarks,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge!.copyWith(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16.0),
                Text(L10n.of(context).no_bookmarks_tip),
              ],
            ),
          );
        }
        return ListView.builder(
          itemCount: bookmarks.length,
          itemBuilder: (context, index) {
            final bookmark = bookmarks[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: BookmarkItem(
                bookmark: bookmark,
                onTap: (cfi) {
                  widget.epubPlayerKey.currentState?.goToCfi(cfi);
                },
                onDelete: (id) {
                  ref
                      .read(BookmarkProvider(bookId).notifier)
                      .removeBookmark(id: id);
                },
              ),
            );
          },
        );
      },
      error: (error, stackTrace) {
        return errorHandler(error, stack: stackTrace);
      },
      loading: () {
        return const Center(child: CircularProgressIndicator());
      },
    );
  }
}

class BookmarkItem extends StatelessWidget {
  const BookmarkItem({
    super.key,
    required this.bookmark,
    required this.onTap,
    required this.onDelete,
  });

  final BookmarkModel bookmark;
  final void Function(String) onTap;
  final void Function(int) onDelete;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(bookmark.cfi),
      child: LayoutUtils.cardContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bookmark content text
            Text(
              bookmark.content.isNotEmpty ? bookmark.content : 'Bookmark',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontSize: 16),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            const Divider(height: 1),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Chapter information
                      Text(
                        bookmark.chapter.isNotEmpty
                            ? bookmark.chapter
                            : 'Chapter',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 2),
                      // Percentage information
                      Text(
                        '${bookmark.percentage.toStringAsFixed(2)}%',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                DeleteConfirm(
                  delete: () {
                    if (bookmark.id != null) {
                      onDelete(bookmark.id!);
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
